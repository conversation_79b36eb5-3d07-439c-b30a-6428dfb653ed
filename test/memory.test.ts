import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'
import { ContextBuilder } from '../bot/service/moer/components/agent/context'
import { chatHistoryWithRoleAndDateListToString } from '../bot/service/moer/components/flow/helper/slotsExtract'
import dayjs from 'dayjs'
import { LLM } from '../bot/lib/ai/llm/LLM'
import logger from '../bot/model/logger/logger'
import { DateHelper } from '../bot/lib/date/date'
import { DataService } from '../bot/service/moer/getter/getData'
import { getPrompt } from '../bot/service/moer/components/agent/prompt'
import { UUID } from '../bot/lib/uuid/uuid'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    // 5 轮一次， 将之前的结果，放到下一次的输入中
    const chat_ids = ['7881300794909689_1688857003605938']
    const roundId = UUID.v4()

    let prevMemory = ''

    for (const chatId of chat_ids) {
      const chat_history =  await ChatHistoryService.getChatHistoryByChatId(chatId)
      let userMessageCount = 0

      let cursor = 0

      for (let i = 0; i < chat_history.length ; i++) {
        if (chat_history[i].role === 'user') {
          userMessageCount += 1
        } else {
          continue
        }

        if (userMessageCount % 5 === 0) {
          // 处理聊天记录
          const chatHistory = chat_history.slice(cursor, i)

          const dialogHistory = chatHistory.map((message) => ({ role:message.role, date:dayjs(message.created_at).format('YYYY/MM/DD HH:mm:ss'), message:message.content }))

          cursor += 1

          const chatHistoryStr = chatHistoryWithRoleAndDateListToString(dialogHistory)

          // mock Temporal Info
          const courseStartTime = await DataService.getCourseStartTime(chatId)

          const currentDate = chatHistory[i].created_at
          // 以当前时间 和 开课时间，反向计算出时间信息

          const temporalInformation =  `- 当前时间：${DateHelper.getFormattedDate(currentDate, true)}，${DateHelper.getTimeOfDay(currentDate)} ${await this.getCourseTimeInfo(chatId)}
- 上课时间：${DateHelper.getFormattedDate(courseStartTime, false)}开始到周三每晚20:00`

          const recorderPrompt = await getPrompt('recorder')

          // 客户记忆
          const memoryOutput = await LLM.predict(
            recorderPrompt, {
              model: 'gpt-5-mini',
              maxTokens: 4096,
              responseJSON: true,
              meta: {
                promptName: 'recorder',
                chat_id: chatId,
                round_id: roundId
              } }, {
              customerMemory: prevMemory,
              dialogHistory: chatHistoryStr,
              temporalInformation: temporalInformation,
            })

          const parsedOutput = JSON.parse(memoryOutput)
          const portrait = parsedOutput['客户记忆'] || {}

          prevMemory = JSON.stringify(portrait, null, 2)
          logger.log(JSON.stringify({
            input: {
              customerMemory: prevMemory,
              dialogHistory: chatHistoryStr,
              temporalInformation: temporalInformation,
            },
            output: portrait,
            modelOutput: parsedOutput
          }, null, 4))
        }
      }

    }
  })
})